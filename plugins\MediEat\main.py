import asyncio
import json
import os
import sqlite3
import time
import tomllib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import aiohttp
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, schedule
from utils.plugin_base import PluginBase


class MediEat(PluginBase):
    description = "智能膳食建议和服药提醒插件"
    author = "AI Assistant"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        
        # 加载配置
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)
            
            # 基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", False)
            self.trigger_word = basic_config.get("trigger_word", "扁鹊")
            
            # AI配置
            ai_config = config.get("ai_config", {})
            self.api_url = ai_config.get("api_url", "http://localhost:11434/api/generate")
            self.api_key = ai_config.get("api_key", "")
            self.model_name = ai_config.get("model_name", "llama3.1:8b")
            self.timeout = ai_config.get("timeout", 30)
            
            # 服药提醒配置
            med_config = config.get("medication_reminder", {})
            self.med_reminder_enable = med_config.get("enable", True)
            self.repeat_interval = med_config.get("repeat_interval", 10)
            self.max_repeat_count = med_config.get("max_repeat_count", 3)
            
            # 体重提醒配置
            weight_config = config.get("weight_reminder", {})
            self.weight_reminder_enable = weight_config.get("enable", True)
            self.weight_reminder_interval = weight_config.get("reminder_interval", 7)
            self.weight_reminder_time = weight_config.get("reminder_time", "09:00")
            
            # 膳食分析配置
            meal_config = config.get("meal_analysis", {})
            self.meal_analysis_enable = meal_config.get("enable", True)
            self.detailed_analysis = meal_config.get("detailed_analysis", True)
            self.provide_suggestions = meal_config.get("provide_suggestions", True)
            
        except Exception as e:
            logger.error(f"加载MediEat配置文件失败: {str(e)}")
            self.enable = False
        
        # 数据目录
        self.data_dir = "medieat_data"
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 初始化数据库
        self.init_databases()
        
        # 待确认的提醒记录
        self.pending_confirmations = {}

    def init_databases(self):
        """初始化数据库表"""
        # 用户信息表
        self.create_user_info_table()
        # 服药提醒表
        self.create_medication_table()
        # 餐饮记录表
        self.create_meal_table()
        # 体重记录表
        self.create_weight_table()
        # 血糖记录表
        self.create_blood_sugar_table()

    def get_db_path(self, table_name: str) -> str:
        """获取数据库文件路径"""
        return os.path.join(self.data_dir, f"{table_name}.db")

    def create_user_info_table(self):
        """创建用户信息表"""
        db_path = self.get_db_path("user_info")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_info (
                    wxid TEXT PRIMARY KEY,
                    gender TEXT,
                    age INTEGER,
                    weight REAL,
                    height REAL,
                    created_time TEXT,
                    updated_time TEXT
                )
            """)
            conn.commit()
        except sqlite3.Error as e:
            logger.error(f"创建用户信息表失败: {e}")
        finally:
            conn.close()

    def create_medication_table(self):
        """创建服药提醒表"""
        db_path = self.get_db_path("medication")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS medication_reminders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    wxid TEXT NOT NULL,
                    medication_name TEXT NOT NULL,
                    reminder_time TEXT NOT NULL,
                    chat_id TEXT NOT NULL,
                    is_active INTEGER DEFAULT 1,
                    created_time TEXT,
                    last_reminded TEXT,
                    confirmation_count INTEGER DEFAULT 0
                )
            """)
            conn.commit()
        except sqlite3.Error as e:
            logger.error(f"创建服药提醒表失败: {e}")
        finally:
            conn.close()

    def create_meal_table(self):
        """创建餐饮记录表"""
        db_path = self.get_db_path("meals")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS meal_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    wxid TEXT NOT NULL,
                    meal_type TEXT NOT NULL,
                    meal_content TEXT NOT NULL,
                    meal_date TEXT NOT NULL,
                    chat_id TEXT NOT NULL,
                    created_time TEXT
                )
            """)
            conn.commit()
        except sqlite3.Error as e:
            logger.error(f"创建餐饮记录表失败: {e}")
        finally:
            conn.close()

    def create_weight_table(self):
        """创建体重记录表"""
        db_path = self.get_db_path("weight")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS weight_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    wxid TEXT NOT NULL,
                    weight REAL NOT NULL,
                    record_date TEXT NOT NULL,
                    created_time TEXT
                )
            """)

            cursor.execute("""
                CREATE TABLE IF NOT EXISTS weight_reminders (
                    wxid TEXT PRIMARY KEY,
                    last_reminder_date TEXT,
                    next_reminder_date TEXT
                )
            """)
            conn.commit()
        except sqlite3.Error as e:
            logger.error(f"创建体重记录表失败: {e}")
        finally:
            conn.close()

    def create_blood_sugar_table(self):
        """创建血糖记录表"""
        db_path = self.get_db_path("blood_sugar")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS blood_sugar_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    wxid TEXT NOT NULL,
                    blood_sugar_value REAL NOT NULL,
                    measurement_type TEXT NOT NULL,
                    record_date TEXT NOT NULL,
                    record_time TEXT NOT NULL,
                    created_time TEXT,
                    notes TEXT
                )
            """)
            conn.commit()
        except sqlite3.Error as e:
            logger.error(f"创建血糖记录表失败: {e}")
        finally:
            conn.close()

    # ==================== 血糖管理功能 ====================

    async def save_blood_sugar_record(self, wxid: str, blood_sugar_value: float,
                                    measurement_type: str, notes: str = None) -> bool:
        """保存血糖记录"""
        db_path = self.get_db_path("blood_sugar")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            current_time = datetime.now()
            record_date = current_time.strftime('%Y-%m-%d')
            record_time = current_time.strftime('%H:%M')
            created_time = current_time.strftime('%Y-%m-%d %H:%M:%S')

            cursor.execute("""
                INSERT INTO blood_sugar_records
                (wxid, blood_sugar_value, measurement_type, record_date, record_time, created_time, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (wxid, blood_sugar_value, measurement_type, record_date, record_time, created_time, notes))

            conn.commit()
            logger.info(f"用户 {wxid} 血糖记录保存成功: {blood_sugar_value} mmol/L ({measurement_type})")
            return True

        except sqlite3.Error as e:
            logger.error(f"保存血糖记录失败: {e}")
            return False
        finally:
            conn.close()

    async def get_blood_sugar_records(self, wxid: str, days: int = 7) -> List[Dict]:
        """获取血糖记录"""
        db_path = self.get_db_path("blood_sugar")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            cursor.execute("""
                SELECT blood_sugar_value, measurement_type, record_date, record_time, notes
                FROM blood_sugar_records
                WHERE wxid = ? AND record_date >= ? AND record_date <= ?
                ORDER BY record_date DESC, record_time DESC
            """, (wxid, start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))

            results = cursor.fetchall()
            records = []
            for blood_sugar_value, measurement_type, record_date, record_time, notes in results:
                records.append({
                    'value': blood_sugar_value,
                    'type': measurement_type,
                    'date': record_date,
                    'time': record_time,
                    'notes': notes
                })

            return records

        except sqlite3.Error as e:
            logger.error(f"获取血糖记录失败: {e}")
            return []
        finally:
            conn.close()

    def parse_blood_sugar_input(self, content: str) -> Dict:
        """解析血糖输入"""
        import re
        result = {}

        # 提取血糖数值
        value_match = re.search(r'(\d+(?:\.\d+)?)', content)
        if value_match:
            result['value'] = float(value_match.group(1))

        # 判断测量类型 - 餐1血糖=餐后1小时，餐2血糖=餐后2小时
        content_lower = content.lower()
        if any(keyword in content_lower for keyword in ['空腹', '餐前', '早上空腹', '晨起']):
            result['type'] = '空腹血糖'
        elif any(keyword in content_lower for keyword in ['餐1', '餐后1小时', '餐后1', '1小时', '饭后1小时']):
            result['type'] = '餐1血糖'
        elif any(keyword in content_lower for keyword in ['餐2', '餐后2小时', '餐后2', '2小时', '饭后2小时']):
            result['type'] = '餐2血糖'
        elif any(keyword in content_lower for keyword in ['睡前', '晚上睡前', '临睡前']):
            result['type'] = '睡前血糖'
        elif any(keyword in content_lower for keyword in ['餐后', '饭后']):
            # 如果只说餐后，需要用户明确是1小时还是2小时，默认为2小时
            result['type'] = '餐2血糖'
        else:
            # 根据时间自动判断
            current_hour = datetime.now().hour
            if 6 <= current_hour < 8:
                result['type'] = '空腹血糖'
            elif 8 <= current_hour < 10:
                result['type'] = '餐1血糖'  # 早餐后1小时
            elif 10 <= current_hour < 14:
                result['type'] = '餐2血糖'  # 早餐后2小时或午餐后
            elif 14 <= current_hour < 16:
                result['type'] = '餐1血糖'  # 午餐后1小时
            elif 16 <= current_hour < 21:
                result['type'] = '餐2血糖'  # 午餐后2小时或晚餐后
            else:
                result['type'] = '睡前血糖'

        return result

    def analyze_blood_sugar_level(self, value: float, measurement_type: str) -> str:
        """分析血糖水平"""
        if measurement_type == '空腹血糖':
            if value < 3.9:
                return "⚠️ 血糖偏低，请立即补充糖分并咨询医生"
            elif value <= 6.1:
                return "✅ 空腹血糖正常"
            elif value <= 7.0:
                return "⚠️ 空腹血糖偏高，需要注意饮食控制"
            else:
                return "🚨 空腹血糖过高，建议立即咨询医生"
        elif measurement_type == '餐1血糖':  # 餐后1小时
            if value < 3.9:
                return "⚠️ 血糖偏低，请立即补充糖分并咨询医生"
            elif value <= 9.0:
                return "✅ 餐后1小时血糖正常"
            elif value <= 11.1:
                return "⚠️ 餐后1小时血糖偏高，需要注意饮食控制"
            else:
                return "🚨 餐后1小时血糖过高，建议立即咨询医生"
        elif measurement_type == '餐2血糖':  # 餐后2小时
            if value < 3.9:
                return "⚠️ 血糖偏低，请立即补充糖分并咨询医生"
            elif value <= 7.8:
                return "✅ 餐后2小时血糖正常"
            elif value <= 11.1:
                return "⚠️ 餐后2小时血糖偏高，需要注意饮食控制"
            else:
                return "🚨 餐后2小时血糖过高，建议立即咨询医生"
        elif measurement_type == '睡前血糖':
            if value < 3.9:
                return "⚠️ 血糖偏低，请立即补充糖分并咨询医生"
            elif value <= 8.0:
                return "✅ 睡前血糖正常"
            elif value <= 10.0:
                return "⚠️ 睡前血糖偏高，需要注意饮食控制"
            else:
                return "🚨 睡前血糖过高，建议立即咨询医生"
        else:
            # 兼容旧格式
            if value < 3.9:
                return "⚠️ 血糖偏低，请立即补充糖分并咨询医生"
            elif value <= 7.8:
                return "✅ 血糖正常"
            elif value <= 11.1:
                return "⚠️ 血糖偏高，需要注意饮食控制"
            else:
                return "🚨 血糖过高，建议立即咨询医生"

    # ==================== 用户信息管理功能 ====================

    async def save_user_info(self, wxid: str, gender: str = None, age: int = None,
                           weight: float = None, height: float = None) -> bool:
        """保存或更新用户信息"""
        db_path = self.get_db_path("user_info")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 检查用户是否已存在
            cursor.execute("SELECT * FROM user_info WHERE wxid = ?", (wxid,))
            existing_user = cursor.fetchone()

            if existing_user:
                # 更新现有用户信息
                update_fields = []
                update_values = []

                if gender is not None:
                    update_fields.append("gender = ?")
                    update_values.append(gender)
                if age is not None:
                    update_fields.append("age = ?")
                    update_values.append(age)
                if weight is not None:
                    update_fields.append("weight = ?")
                    update_values.append(weight)
                if height is not None:
                    update_fields.append("height = ?")
                    update_values.append(height)

                if update_fields:
                    update_fields.append("updated_time = ?")
                    update_values.append(current_time)
                    update_values.append(wxid)

                    sql = f"UPDATE user_info SET {', '.join(update_fields)} WHERE wxid = ?"
                    cursor.execute(sql, update_values)
            else:
                # 创建新用户
                cursor.execute("""
                    INSERT INTO user_info (wxid, gender, age, weight, height, created_time, updated_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (wxid, gender, age, weight, height, current_time, current_time))

            conn.commit()
            logger.info(f"用户 {wxid} 信息保存成功")
            return True

        except sqlite3.Error as e:
            logger.error(f"保存用户信息失败: {e}")
            return False
        finally:
            conn.close()

    async def get_user_info(self, wxid: str) -> Optional[Dict]:
        """获取用户信息"""
        db_path = self.get_db_path("user_info")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT * FROM user_info WHERE wxid = ?", (wxid,))
            result = cursor.fetchone()

            if result:
                return {
                    'wxid': result[0],
                    'gender': result[1],
                    'age': result[2],
                    'weight': result[3],
                    'height': result[4],
                    'created_time': result[5],
                    'updated_time': result[6]
                }
            return None

        except sqlite3.Error as e:
            logger.error(f"获取用户信息失败: {e}")
            return None
        finally:
            conn.close()

    async def parse_user_info_input(self, content: str) -> Dict:
        """解析用户输入的个人信息"""
        info = {}
        content = content.lower()

        # 解析性别
        if '男' in content or 'male' in content:
            info['gender'] = '男'
        elif '女' in content or 'female' in content:
            info['gender'] = '女'

        # 解析年龄
        import re
        age_match = re.search(r'(\d+)\s*岁|年龄\s*(\d+)|age\s*(\d+)', content)
        if age_match:
            age = age_match.group(1) or age_match.group(2) or age_match.group(3)
            info['age'] = int(age)

        # 解析体重
        weight_match = re.search(r'(\d+(?:\.\d+)?)\s*kg|(\d+(?:\.\d+)?)\s*公斤|体重\s*(\d+(?:\.\d+)?)', content)
        if weight_match:
            weight = weight_match.group(1) or weight_match.group(2) or weight_match.group(3)
            info['weight'] = float(weight)

        # 解析身高
        height_match = re.search(r'(\d+(?:\.\d+)?)\s*cm|(\d+(?:\.\d+)?)\s*厘米|身高\s*(\d+(?:\.\d+)?)', content)
        if height_match:
            height = height_match.group(1) or height_match.group(2) or height_match.group(3)
            info['height'] = float(height)

        return info

    # ==================== 服药提醒功能 ====================

    async def add_medication_reminder(self, wxid: str, medication_name: str,
                                    reminder_time: str, chat_id: str) -> bool:
        """添加服药提醒"""
        db_path = self.get_db_path("medication")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute("""
                INSERT INTO medication_reminders
                (wxid, medication_name, reminder_time, chat_id, created_time)
                VALUES (?, ?, ?, ?, ?)
            """, (wxid, medication_name, reminder_time, chat_id, current_time))

            conn.commit()
            reminder_id = cursor.lastrowid
            logger.info(f"用户 {wxid} 添加服药提醒成功: {medication_name} at {reminder_time}")
            return reminder_id

        except sqlite3.Error as e:
            logger.error(f"添加服药提醒失败: {e}")
            return False
        finally:
            conn.close()

    async def get_medication_reminders(self, wxid: str) -> List[Tuple]:
        """获取用户的服药提醒列表"""
        db_path = self.get_db_path("medication")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("""
                SELECT id, medication_name, reminder_time, is_active
                FROM medication_reminders
                WHERE wxid = ? AND is_active = 1
            """, (wxid,))
            return cursor.fetchall()

        except sqlite3.Error as e:
            logger.error(f"获取服药提醒失败: {e}")
            return []
        finally:
            conn.close()

    async def delete_medication_reminder(self, wxid: str, reminder_id: int) -> bool:
        """删除服药提醒"""
        db_path = self.get_db_path("medication")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("""
                UPDATE medication_reminders
                SET is_active = 0
                WHERE id = ? AND wxid = ?
            """, (reminder_id, wxid))

            conn.commit()
            logger.info(f"用户 {wxid} 删除服药提醒 {reminder_id} 成功")
            return True

        except sqlite3.Error as e:
            logger.error(f"删除服药提醒失败: {e}")
            return False
        finally:
            conn.close()

    async def update_reminder_confirmation(self, reminder_id: int, confirmed: bool = True):
        """更新提醒确认状态"""
        db_path = self.get_db_path("medication")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            if confirmed:
                cursor.execute("""
                    UPDATE medication_reminders
                    SET last_reminded = ?, confirmation_count = 0
                    WHERE id = ?
                """, (current_time, reminder_id))
            else:
                cursor.execute("""
                    UPDATE medication_reminders
                    SET confirmation_count = confirmation_count + 1
                    WHERE id = ?
                """, (reminder_id,))

            conn.commit()

        except sqlite3.Error as e:
            logger.error(f"更新提醒确认状态失败: {e}")
        finally:
            conn.close()

    # ==================== 餐饮档案管理功能 ====================

    async def save_meal_record(self, wxid: str, meal_type: str, meal_content: str,
                             chat_id: str, meal_date: str = None) -> bool:
        """保存餐饮记录"""
        if meal_date is None:
            meal_date = datetime.now().strftime('%Y-%m-%d')

        db_path = self.get_db_path("meals")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 检查当天是否已有相同类型的餐饮记录
            cursor.execute("""
                SELECT id FROM meal_records
                WHERE wxid = ? AND meal_type = ? AND meal_date = ?
            """, (wxid, meal_type, meal_date))

            existing_record = cursor.fetchone()

            if existing_record:
                # 更新现有记录
                cursor.execute("""
                    UPDATE meal_records
                    SET meal_content = ?, created_time = ?
                    WHERE id = ?
                """, (meal_content, current_time, existing_record[0]))
            else:
                # 创建新记录
                cursor.execute("""
                    INSERT INTO meal_records
                    (wxid, meal_type, meal_content, meal_date, chat_id, created_time)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (wxid, meal_type, meal_content, meal_date, chat_id, current_time))

            conn.commit()
            logger.info(f"用户 {wxid} 保存{meal_type}记录成功: {meal_content}")
            return True

        except sqlite3.Error as e:
            logger.error(f"保存餐饮记录失败: {e}")
            return False
        finally:
            conn.close()

    async def get_daily_meals(self, wxid: str, meal_date: str = None) -> Dict[str, str]:
        """获取指定日期的餐饮记录"""
        if meal_date is None:
            meal_date = datetime.now().strftime('%Y-%m-%d')

        db_path = self.get_db_path("meals")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("""
                SELECT meal_type, meal_content
                FROM meal_records
                WHERE wxid = ? AND meal_date = ?
            """, (wxid, meal_date))

            results = cursor.fetchall()
            meals = {}
            for meal_type, meal_content in results:
                meals[meal_type] = meal_content

            return meals

        except sqlite3.Error as e:
            logger.error(f"获取餐饮记录失败: {e}")
            return {}
        finally:
            conn.close()

    async def get_recent_meals(self, wxid: str, days: int = 7) -> List[Dict]:
        """获取最近几天的餐饮记录"""
        db_path = self.get_db_path("meals")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            cursor.execute("""
                SELECT meal_date, meal_type, meal_content
                FROM meal_records
                WHERE wxid = ? AND meal_date >= ? AND meal_date <= ?
                ORDER BY meal_date DESC, meal_type
            """, (wxid, start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))

            results = cursor.fetchall()
            meals = []
            for meal_date, meal_type, meal_content in results:
                meals.append({
                    'date': meal_date,
                    'type': meal_type,
                    'content': meal_content
                })

            return meals

        except sqlite3.Error as e:
            logger.error(f"获取最近餐饮记录失败: {e}")
            return []
        finally:
            conn.close()

    def parse_meal_type(self, content: str) -> str:
        """解析餐饮类型"""
        content = content.lower()
        if '早餐' in content or '早饭' in content or '早上' in content:
            return '早餐'
        elif '午餐' in content or '午饭' in content or '中午' in content:
            return '午餐'
        elif '晚餐' in content or '晚饭' in content or '晚上' in content:
            return '晚餐'
        else:
            # 根据时间判断
            current_hour = datetime.now().hour
            if 5 <= current_hour < 11:
                return '早餐'
            elif 11 <= current_hour < 17:
                return '午餐'
            else:
                return '晚餐'

    # ==================== AI膳食建议功能 ====================

    def get_nutrition_prompt(self, user_info: Dict, daily_meals: Dict, meal_type: str = None) -> str:
        """生成营养建议的prompt"""

        base_prompt = """你是一位专业的营养师，专门为糖尿病和高血脂患者提供膳食建议。请根据以下用户信息和当日饮食记录，提供专业的营养建议。

用户信息：
"""

        if user_info:
            if user_info.get('gender'):
                base_prompt += f"- 性别：{user_info['gender']}\n"
            if user_info.get('age'):
                base_prompt += f"- 年龄：{user_info['age']}岁\n"
            if user_info.get('weight'):
                base_prompt += f"- 体重：{user_info['weight']}kg\n"
            if user_info.get('height'):
                base_prompt += f"- 身高：{user_info['height']}cm\n"

                # 计算BMI
                if user_info.get('weight') and user_info.get('height'):
                    bmi = user_info['weight'] / ((user_info['height'] / 100) ** 2)
                    base_prompt += f"- BMI：{bmi:.1f}\n"

        base_prompt += "\n当日饮食记录：\n"

        if daily_meals:
            for meal_type_key, meal_content in daily_meals.items():
                base_prompt += f"- {meal_type_key}：{meal_content}\n"
        else:
            base_prompt += "- 暂无记录\n"

        if meal_type:
            base_prompt += f"\n用户正在询问关于{meal_type}的建议。"

        base_prompt += """

请提供以下建议：
1. 对当前饮食的营养评估（特别关注血糖和血脂控制）
2. 具体的改进建议
3. 推荐的食物搭配
4. 需要避免的食物
5. 饮食时间和分量建议

请用简洁明了的语言回答，重点关注控糖降脂的目标。回答控制在200字以内。"""

        return base_prompt

    async def get_ai_nutrition_advice(self, user_info: Dict, daily_meals: Dict,
                                    meal_type: str = None) -> str:
        """获取AI营养建议"""
        max_retries = 3
        retry_delay = 2  # 秒

        for attempt in range(max_retries):
            try:
                prompt = self.get_nutrition_prompt(user_info, daily_meals, meal_type)

                # 构建OpenAI兼容的请求数据
                request_data = {
                    "model": self.model_name,
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": 500,
                    "stream": False
                }

                headers = {
                    "Content-Type": "application/json"
                }

                if self.api_key:
                    headers["Authorization"] = f"Bearer {self.api_key}"

                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                    async with session.post(self.api_url, json=request_data, headers=headers) as response:
                        if response.status == 200:
                            result = await response.json()
                            if 'choices' in result and len(result['choices']) > 0:
                                return result['choices'][0]['message']['content'].strip()
                            else:
                                logger.error(f"AI响应格式错误: {result}")
                                return self.get_fallback_advice(user_info, daily_meals, meal_type)
                        elif response.status == 429:
                            logger.warning(f"AI请求频率限制，第{attempt + 1}次尝试，等待{retry_delay}秒后重试")
                            if attempt < max_retries - 1:
                                await asyncio.sleep(retry_delay)
                                retry_delay *= 2  # 指数退避
                                continue
                            else:
                                return self.get_fallback_advice(user_info, daily_meals, meal_type)
                        else:
                            logger.error(f"AI请求失败，状态码: {response.status}")
                            if attempt < max_retries - 1:
                                await asyncio.sleep(retry_delay)
                                continue
                            else:
                                return self.get_fallback_advice(user_info, daily_meals, meal_type)

            except asyncio.TimeoutError:
                logger.error(f"AI请求超时，第{attempt + 1}次尝试")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    return self.get_fallback_advice(user_info, daily_meals, meal_type)
            except Exception as e:
                logger.error(f"获取AI建议失败，第{attempt + 1}次尝试: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    return self.get_fallback_advice(user_info, daily_meals, meal_type)

        # 如果所有重试都失败了
        return self.get_fallback_advice(user_info, daily_meals, meal_type)

    def get_fallback_advice(self, user_info: Dict, daily_meals: Dict, meal_type: str = None) -> str:
        """当AI服务不可用时的备用建议"""
        advice = "🍽️ 膳食建议（基于基础规则）\n\n"

        # 基于用户信息给出基础建议
        if user_info:
            bmi = None
            if user_info.get('weight') and user_info.get('height'):
                bmi = user_info['weight'] / ((user_info['height'] / 100) ** 2)

            advice += "📊 基础评估：\n"
            if bmi:
                if bmi < 18.5:
                    advice += "• 体重偏轻，建议适量增加优质蛋白质摄入\n"
                elif bmi > 24:
                    advice += "• 体重偏重，建议控制总热量摄入\n"
                else:
                    advice += "• 体重正常，保持均衡饮食\n"

        advice += "\n🥗 控糖降脂建议：\n"
        advice += "• 选择低GI食物：燕麦、糙米、全麦面包\n"
        advice += "• 优质蛋白质：鱼类、瘦肉、豆制品、鸡蛋\n"
        advice += "• 健康脂肪：坚果、橄榄油、深海鱼\n"
        advice += "• 多吃蔬菜：绿叶菜、十字花科蔬菜\n"
        advice += "• 避免：精制糖、油炸食品、加工肉类\n"

        if meal_type:
            advice += f"\n⏰ {meal_type}特别提醒：\n"
            if meal_type == "早餐":
                advice += "• 确保有优质蛋白质和复合碳水化合物\n"
                advice += "• 可以适量添加坚果或种子\n"
            elif meal_type == "午餐":
                advice += "• 注意荤素搭配，蔬菜占一半\n"
                advice += "• 选择瘦肉或鱼类作为蛋白质来源\n"
            elif meal_type == "晚餐":
                advice += "• 减少碳水化合物摄入\n"
                advice += "• 以蔬菜和蛋白质为主\n"

        advice += "\n💡 AI服务暂时不可用，以上为基础建议，请咨询专业营养师获得个性化指导。"
        return advice

    async def get_blood_sugar_advice(self, user_info: Dict, daily_meals: Dict,
                                   blood_sugar_context: str) -> str:
        """获取血糖相关的AI建议"""
        try:
            prompt = f"""你是一位专业的内分泌科医生和营养师，专门为糖尿病患者提供血糖管理建议。

用户基础信息：
"""

            if user_info:
                if user_info.get('gender'):
                    prompt += f"- 性别：{user_info['gender']}\n"
                if user_info.get('age'):
                    prompt += f"- 年龄：{user_info['age']}岁\n"
                if user_info.get('weight'):
                    prompt += f"- 体重：{user_info['weight']}kg\n"
                if user_info.get('height'):
                    prompt += f"- 身高：{user_info['height']}cm\n"

                    # 计算BMI
                    if user_info.get('weight') and user_info.get('height'):
                        bmi = user_info['weight'] / ((user_info['height'] / 100) ** 2)
                        prompt += f"- BMI：{bmi:.1f}\n"

            prompt += f"\n今日饮食记录：\n"
            if daily_meals:
                for meal_type, meal_content in daily_meals.items():
                    prompt += f"- {meal_type}：{meal_content}\n"
            else:
                prompt += "- 暂无饮食记录\n"

            prompt += f"\n血糖监测信息：\n{blood_sugar_context}\n"

            prompt += """
重要说明：
- 餐1血糖 = 餐后1小时血糖（正常范围：≤9.0 mmol/L）
- 餐2血糖 = 餐后2小时血糖（正常范围：≤7.8 mmol/L）
- 空腹血糖正常范围：3.9-6.1 mmol/L
- 睡前血糖正常范围：≤8.0 mmol/L

请基于以上完整信息提供专业建议：
1. 对当前血糖水平的专业评估
2. 结合今日饮食分析血糖波动原因
3. 针对性的下一餐饮食建议
4. 血糖控制的具体注意事项
5. 是否需要调整饮食或就医建议

请用专业但易懂的语言回答，重点关注血糖控制和饮食管理。回答控制在250字以内。"""

            # 构建OpenAI兼容的请求数据
            request_data = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.7,
                "top_p": 0.9,
                "max_tokens": 500,
                "stream": False
            }

            headers = {
                "Content-Type": "application/json"
            }

            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(self.api_url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        if 'choices' in result and len(result['choices']) > 0:
                            return result['choices'][0]['message']['content'].strip()

            # 如果AI不可用，返回基础建议
            return self.get_fallback_blood_sugar_advice(blood_sugar_context)

        except Exception as e:
            logger.error(f"获取血糖AI建议失败: {e}")
            return self.get_fallback_blood_sugar_advice(blood_sugar_context)

    def get_fallback_blood_sugar_advice(self, blood_sugar_context: str) -> str:
        """血糖管理的备用建议"""
        advice = "📋 血糖管理建议（基础指导）：\n\n"
        advice += "🍽️ 饮食建议：\n"
        advice += "• 选择低GI食物，避免血糖快速上升\n"
        advice += "• 控制碳水化合物摄入量\n"
        advice += "• 增加膳食纤维，延缓糖分吸收\n"
        advice += "• 少食多餐，避免血糖波动\n\n"
        advice += "⚠️ 注意事项：\n"
        advice += "• 定期监测血糖变化\n"
        advice += "• 如血糖持续异常，请及时就医\n"
        advice += "• 遵医嘱服药，不可自行调整\n\n"
        advice += "💡 AI服务暂时不可用，建议咨询专业医生获得个性化指导。"
        return advice

    async def provide_meal_suggestion(self, wxid: str, meal_type: str = None) -> str:
        """提供膳食建议"""
        # 获取用户信息
        user_info = await self.get_user_info(wxid)

        # 获取当日餐饮记录
        daily_meals = await self.get_daily_meals(wxid)

        if not user_info:
            return "请先设置您的个人信息（性别、年龄、体重、身高），这样我可以为您提供更准确的膳食建议。\n\n发送格式：设置信息 男 25岁 70kg 175cm"

        # 获取AI建议
        ai_advice = await self.get_ai_nutrition_advice(user_info, daily_meals, meal_type)

        return f"🍽️ 个性化膳食建议\n\n{ai_advice}"

    # ==================== 体重管理功能 ====================

    async def save_weight_record(self, wxid: str, weight: float) -> bool:
        """保存体重记录"""
        db_path = self.get_db_path("weight")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            current_date = datetime.now().strftime('%Y-%m-%d')

            # 保存体重记录
            cursor.execute("""
                INSERT INTO weight_records (wxid, weight, record_date, created_time)
                VALUES (?, ?, ?, ?)
            """, (wxid, weight, current_date, current_time))

            # 更新用户信息表中的体重
            await self.save_user_info(wxid, weight=weight)

            # 更新下次提醒时间
            next_reminder = datetime.now() + timedelta(days=self.weight_reminder_interval)
            cursor.execute("""
                INSERT OR REPLACE INTO weight_reminders (wxid, last_reminder_date, next_reminder_date)
                VALUES (?, ?, ?)
            """, (wxid, current_date, next_reminder.strftime('%Y-%m-%d')))

            conn.commit()
            logger.info(f"用户 {wxid} 体重记录保存成功: {weight}kg")
            return True

        except sqlite3.Error as e:
            logger.error(f"保存体重记录失败: {e}")
            return False
        finally:
            conn.close()

    async def get_weight_history(self, wxid: str, days: int = 30) -> List[Dict]:
        """获取体重历史记录"""
        db_path = self.get_db_path("weight")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            cursor.execute("""
                SELECT weight, record_date
                FROM weight_records
                WHERE wxid = ? AND record_date >= ? AND record_date <= ?
                ORDER BY record_date DESC
            """, (wxid, start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))

            results = cursor.fetchall()
            history = []
            for weight, record_date in results:
                history.append({
                    'weight': weight,
                    'date': record_date
                })

            return history

        except sqlite3.Error as e:
            logger.error(f"获取体重历史失败: {e}")
            return []
        finally:
            conn.close()

    async def check_weight_reminder_needed(self, wxid: str) -> bool:
        """检查是否需要体重提醒"""
        db_path = self.get_db_path("weight")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("""
                SELECT next_reminder_date FROM weight_reminders WHERE wxid = ?
            """, (wxid,))

            result = cursor.fetchone()
            if result:
                next_reminder_date = datetime.strptime(result[0], '%Y-%m-%d').date()
                today = datetime.now().date()
                return today >= next_reminder_date
            else:
                # 如果没有记录，检查用户是否有体重记录
                cursor.execute("""
                    SELECT MAX(record_date) FROM weight_records WHERE wxid = ?
                """, (wxid,))
                last_record = cursor.fetchone()

                if last_record and last_record[0]:
                    last_date = datetime.strptime(last_record[0], '%Y-%m-%d').date()
                    days_since_last = (datetime.now().date() - last_date).days
                    return days_since_last >= self.weight_reminder_interval
                else:
                    # 新用户，需要提醒
                    return True

        except sqlite3.Error as e:
            logger.error(f"检查体重提醒失败: {e}")
            return False
        finally:
            conn.close()

    # ==================== 消息处理功能 ====================

    @on_text_message(priority=60)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return True

        wxid = message["SenderWxid"]
        content = message["Content"].strip()
        chat_id = message["FromWxid"]
        is_group_chat = chat_id.endswith("chatroom")

        # 检查唤醒词
        if not content.startswith(self.trigger_word):
            # 如果不是以唤醒词开头，只处理特定的确认消息
            if content in ["已服药", "服药确认", "确认"]:
                # 处理服药确认
                if wxid in self.pending_confirmations:
                    reminder_id = self.pending_confirmations[wxid]
                    await self.update_reminder_confirmation(reminder_id, True)
                    del self.pending_confirmations[wxid]
                    response = "✅ 服药确认成功"

                    if is_group_chat:
                        await bot.send_at_message(chat_id, response, [wxid])
                    else:
                        await bot.send_text_message(chat_id, response)
                    return False
            return True

        # 移除唤醒词
        content = content[len(self.trigger_word):].strip()
        if not content:
            # 只有唤醒词，显示帮助信息
            help_text = """🍽️ MediEat 智能膳食助手

📋 功能介绍：
• 个人信息管理
• 智能膳食建议（控糖降脂）
• 血糖监测与分析
• 服药提醒
• 餐饮记录与分析

💡 使用方法：
• 扁鹊 设置信息 男 25岁 70kg 175cm
• 扁鹊 更新体重 70kg
• 扁鹊 空腹血糖 5.8 / 餐1血糖 8.2
• 扁鹊 餐2血糖 7.1 / 睡前血糖 6.9
• 扁鹊 血糖记录
• 扁鹊 服药提醒 每日 08:00 药物名称
• 扁鹊 早餐/午餐/晚餐 + 具体内容
• 扁鹊 膳食建议
• 扁鹊 我的信息
• 扁鹊 我的提醒

🎯 专为糖尿病和高血脂患者设计，提供专业的营养和血糖管理建议！"""

            if is_group_chat:
                await bot.send_at_message(chat_id, help_text, [wxid])
            else:
                await bot.send_text_message(chat_id, help_text)
            return False

        try:
            # 设置个人信息
            if content.startswith("设置信息"):
                info_text = content[4:].strip()
                parsed_info = await self.parse_user_info_input(info_text)

                if parsed_info:
                    success = await self.save_user_info(wxid, **parsed_info)
                    if success:
                        response = "✅ 个人信息设置成功！\n\n"
                        user_info = await self.get_user_info(wxid)
                        if user_info:
                            response += "📋 您的信息：\n"
                            if user_info.get('gender'):
                                response += f"性别：{user_info['gender']}\n"
                            if user_info.get('age'):
                                response += f"年龄：{user_info['age']}岁\n"
                            if user_info.get('weight'):
                                response += f"体重：{user_info['weight']}kg\n"
                            if user_info.get('height'):
                                response += f"身高：{user_info['height']}cm\n"
                    else:
                        response = "❌ 信息设置失败，请稍后再试"
                else:
                    response = "❌ 信息格式错误\n\n正确格式：设置信息 男 25岁 70kg 175cm"

                if is_group_chat:
                    await bot.send_at_message(chat_id, response, [wxid])
                else:
                    await bot.send_text_message(chat_id, response)
                return False

            # 更新体重
            elif content.startswith("更新体重"):
                weight_text = content[4:].strip()
                try:
                    import re
                    weight_match = re.search(r'(\d+(?:\.\d+)?)', weight_text)
                    if weight_match:
                        weight = float(weight_match.group(1))
                        success = await self.save_weight_record(wxid, weight)
                        if success:
                            response = f"✅ 体重更新成功：{weight}kg"
                        else:
                            response = "❌ 体重更新失败，请稍后再试"
                    else:
                        response = "❌ 体重格式错误\n\n正确格式：更新体重 70kg 或 更新体重 70"
                except ValueError:
                    response = "❌ 体重格式错误，请输入有效数字"

                if is_group_chat:
                    await bot.send_at_message(chat_id, response, [wxid])
                else:
                    await bot.send_text_message(chat_id, response)
                return False

            # 服药提醒设置
            elif content.startswith("服药提醒"):
                reminder_text = content[4:].strip()
                parts = reminder_text.split()

                if len(parts) >= 3 and parts[0] == "每日":
                    time_str = parts[1]
                    medication_name = " ".join(parts[2:])

                    # 验证时间格式
                    try:
                        datetime.strptime(time_str, "%H:%M")
                        reminder_id = await self.add_medication_reminder(wxid, medication_name, time_str, chat_id)
                        if reminder_id:
                            response = f"✅ 服药提醒设置成功\n\n💊 药物：{medication_name}\n⏰ 时间：每日 {time_str}"
                        else:
                            response = "❌ 服药提醒设置失败，请稍后再试"
                    except ValueError:
                        response = "❌ 时间格式错误\n\n正确格式：服药提醒 每日 08:00 药物名称"
                else:
                    response = "❌ 格式错误\n\n正确格式：服药提醒 每日 08:00 药物名称"

                if is_group_chat:
                    await bot.send_at_message(chat_id, response, [wxid])
                else:
                    await bot.send_text_message(chat_id, response)
                return False

            # 记录血糖
            elif content.startswith("血糖") or "血糖" in content:
                blood_sugar_info = self.parse_blood_sugar_input(content)

                if 'value' in blood_sugar_info:
                    blood_sugar_value = blood_sugar_info['value']
                    measurement_type = blood_sugar_info.get('type', '未知')

                    success = await self.save_blood_sugar_record(wxid, blood_sugar_value, measurement_type, content)
                    if success:
                        analysis = self.analyze_blood_sugar_level(blood_sugar_value, measurement_type)
                        response = f"✅ 血糖记录成功\n\n"
                        response += f"📊 血糖值：{blood_sugar_value} mmol/L\n"
                        response += f"⏰ 测量类型：{measurement_type}\n"
                        response += f"📈 分析结果：{analysis}\n\n"

                        # 获取AI建议
                        if self.meal_analysis_enable:
                            user_info = await self.get_user_info(wxid)
                            daily_meals = await self.get_daily_meals(wxid)
                            blood_sugar_records = await self.get_blood_sugar_records(wxid, 7)

                            # 构建完整的血糖信息上下文
                            blood_sugar_context = f"当前测量：{blood_sugar_value} mmol/L ({measurement_type})"
                            blood_sugar_context += f"\n测量时间：{datetime.now().strftime('%Y-%m-%d %H:%M')}"

                            if blood_sugar_records:
                                blood_sugar_context += "\n\n最近7天血糖记录："
                                # 按类型分组显示
                                types_shown = set()
                                for record in blood_sugar_records[:10]:
                                    if record['type'] not in types_shown or len(types_shown) < 4:
                                        blood_sugar_context += f"\n- {record['date']} {record['time']}: {record['value']} mmol/L ({record['type']})"
                                        types_shown.add(record['type'])

                                # 计算各类型血糖的平均值
                                type_averages = {}
                                for record in blood_sugar_records:
                                    bg_type = record['type']
                                    if bg_type not in type_averages:
                                        type_averages[bg_type] = []
                                    type_averages[bg_type].append(record['value'])

                                if type_averages:
                                    blood_sugar_context += "\n\n血糖类型平均值："
                                    for bg_type, values in type_averages.items():
                                        avg_value = sum(values) / len(values)
                                        blood_sugar_context += f"\n- {bg_type}平均：{avg_value:.1f} mmol/L"

                            ai_advice = await self.get_blood_sugar_advice(user_info, daily_meals, blood_sugar_context)
                            response += f"\n\n🤖 AI专业建议：\n{ai_advice}"
                    else:
                        response = "❌ 血糖记录失败，请稍后再试"
                else:
                    response = "❌ 血糖格式错误\n\n正确格式：\n• 血糖 6.5（自动判断类型）\n• 空腹血糖 5.8\n• 餐后血糖 8.2"

                if is_group_chat:
                    await bot.send_at_message(chat_id, response, [wxid])
                else:
                    await bot.send_text_message(chat_id, response)
                return False

            # 查看血糖记录
            elif content in ["血糖记录", "我的血糖", "血糖历史"]:
                records = await self.get_blood_sugar_records(wxid, 7)
                if records:
                    response = "📊 最近7天血糖记录：\n\n"

                    # 按类型分组显示
                    blood_sugar_types = {}
                    for record in records:
                        bg_type = record['type']
                        if bg_type not in blood_sugar_types:
                            blood_sugar_types[bg_type] = []
                        blood_sugar_types[bg_type].append(record)

                    # 按类型顺序显示
                    type_order = ['空腹血糖', '餐1血糖', '餐2血糖', '睡前血糖']
                    for bg_type in type_order:
                        if bg_type in blood_sugar_types:
                            response += f"🔸 {bg_type}：\n"
                            for record in blood_sugar_types[bg_type][:3]:  # 最近3次
                                analysis = self.analyze_blood_sugar_level(record['value'], record['type'])
                                status_icon = "✅" if "正常" in analysis else "⚠️" if "偏" in analysis else "🚨"
                                response += f"  {status_icon} {record['date']} {record['time']} - {record['value']} mmol/L\n"
                            response += "\n"

                    # 添加整体趋势分析
                    if len(records) >= 3:
                        recent_values = [r['value'] for r in records[:5]]
                        avg_recent = sum(recent_values) / len(recent_values)
                        response += f"📈 最近血糖平均：{avg_recent:.1f} mmol/L\n"

                        # 分析血糖控制情况
                        normal_count = sum(1 for r in records[:10] if "正常" in self.analyze_blood_sugar_level(r['value'], r['type']))
                        total_count = min(len(records), 10)
                        control_rate = (normal_count / total_count) * 100
                        response += f"🎯 血糖达标率：{control_rate:.0f}% ({normal_count}/{total_count})"
                else:
                    response = "❌ 您还没有血糖记录\n\n请发送：血糖 6.5 或 空腹血糖 5.8"

                if is_group_chat:
                    await bot.send_at_message(chat_id, response, [wxid])
                else:
                    await bot.send_text_message(chat_id, response)
                return False

            # 记录餐饮
            elif any(keyword in content for keyword in ['早餐', '午餐', '晚餐', '早饭', '午饭', '晚饭']):
                meal_type = self.parse_meal_type(content)
                meal_content = content

                success = await self.save_meal_record(wxid, meal_type, meal_content, chat_id)
                if success:
                    response = f"✅ {meal_type}记录成功\n\n🍽️ 内容：{meal_content}"

                    # 提供膳食建议
                    if self.meal_analysis_enable:
                        suggestion = await self.provide_meal_suggestion(wxid, meal_type)
                        response += f"\n\n{suggestion}"
                else:
                    response = "❌ 餐饮记录失败，请稍后再试"

                if is_group_chat:
                    await bot.send_at_message(chat_id, response, [wxid])
                else:
                    await bot.send_text_message(chat_id, response)
                return False

            # 膳食建议
            elif content in ["膳食建议", "营养建议", "饮食建议"]:
                suggestion = await self.provide_meal_suggestion(wxid)

                if is_group_chat:
                    await bot.send_at_message(chat_id, suggestion, [wxid])
                else:
                    await bot.send_text_message(chat_id, suggestion)
                return False

            # 查看我的信息
            elif content in ["我的信息", "个人信息"]:
                user_info = await self.get_user_info(wxid)
                if user_info:
                    response = "📋 您的个人信息：\n"
                    if user_info.get('gender'):
                        response += f"性别：{user_info['gender']}\n"
                    if user_info.get('age'):
                        response += f"年龄：{user_info['age']}岁\n"
                    if user_info.get('weight'):
                        response += f"体重：{user_info['weight']}kg\n"
                    if user_info.get('height'):
                        response += f"身高：{user_info['height']}cm\n"

                        # 计算BMI
                        if user_info.get('weight') and user_info.get('height'):
                            bmi = user_info['weight'] / ((user_info['height'] / 100) ** 2)
                            response += f"BMI：{bmi:.1f}\n"
                else:
                    response = "❌ 您还没有设置个人信息\n\n请发送：设置信息 男 25岁 70kg 175cm"

                if is_group_chat:
                    await bot.send_at_message(chat_id, response, [wxid])
                else:
                    await bot.send_text_message(chat_id, response)
                return False

            # 查看服药提醒
            elif content in ["我的提醒", "服药提醒列表"]:
                reminders = await self.get_medication_reminders(wxid)
                if reminders:
                    response = "💊 您的服药提醒：\n\n"
                    for reminder_id, medication_name, reminder_time, is_active in reminders:
                        response += f"🆔 {reminder_id}. {medication_name} - 每日 {reminder_time}\n"
                else:
                    response = "❌ 您还没有设置服药提醒\n\n请发送：服药提醒 每日 08:00 药物名称"

                if is_group_chat:
                    await bot.send_at_message(chat_id, response, [wxid])
                else:
                    await bot.send_text_message(chat_id, response)
                return False

            # 删除服药提醒
            elif content.startswith("删除提醒"):
                try:
                    reminder_id = int(content[4:].strip())
                    success = await self.delete_medication_reminder(wxid, reminder_id)
                    if success:
                        response = f"✅ 服药提醒 {reminder_id} 删除成功"
                    else:
                        response = f"❌ 删除提醒失败，请检查提醒ID"
                except ValueError:
                    response = "❌ 格式错误\n\n正确格式：删除提醒 1"

                if is_group_chat:
                    await bot.send_at_message(chat_id, response, [wxid])
                else:
                    await bot.send_text_message(chat_id, response)
                return False

            # MediEat帮助
            elif content in ["帮助", "使用帮助", "help"]:
                help_text = """🍽️ MediEat 智能膳食助手

📋 功能介绍：
• 个人信息管理
• 智能膳食建议（控糖降脂）
• 血糖监测与分析
• 服药提醒
• 餐饮记录与分析

💡 使用方法：
• 扁鹊 设置信息 男 25岁 70kg 175cm
• 扁鹊 更新体重 70kg
• 扁鹊 空腹血糖 5.8 / 餐1血糖 8.2
• 扁鹊 餐2血糖 7.1 / 睡前血糖 6.9
• 扁鹊 血糖记录
• 扁鹊 服药提醒 每日 08:00 药物名称
• 扁鹊 早餐/午餐/晚餐 + 具体内容
• 扁鹊 膳食建议
• 扁鹊 我的信息
• 扁鹊 我的提醒

🎯 专为糖尿病和高血脂患者设计，提供专业的营养和血糖管理建议！"""

                if is_group_chat:
                    await bot.send_at_message(chat_id, help_text, [wxid])
                else:
                    await bot.send_text_message(chat_id, help_text)
                return False

        except Exception as e:
            logger.error(f"处理MediEat消息时出错: {e}")
            return True

        return True

    # ==================== 定时任务功能 ====================

    @schedule('interval', seconds=60)
    async def check_medication_reminders(self, bot: WechatAPIClient):
        """检查服药提醒"""
        if not self.enable or not self.med_reminder_enable:
            return

        current_time = datetime.now()
        current_time_str = current_time.strftime("%H:%M")

        db_path = self.get_db_path("medication")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            # 查找需要提醒的服药记录
            cursor.execute("""
                SELECT id, wxid, medication_name, reminder_time, chat_id, last_reminded, confirmation_count
                FROM medication_reminders
                WHERE is_active = 1 AND reminder_time = ?
            """, (current_time_str,))

            reminders = cursor.fetchall()

            for reminder_id, wxid, medication_name, reminder_time, chat_id, last_reminded, confirmation_count in reminders:
                # 检查今天是否已经提醒过
                today = current_time.strftime("%Y-%m-%d")
                if last_reminded and last_reminded.startswith(today):
                    continue

                # 发送提醒
                await self.send_medication_reminder(bot, wxid, medication_name, reminder_id, chat_id)

                # 更新提醒状态
                await self.update_reminder_confirmation(reminder_id, False)

                # 添加到待确认列表
                self.pending_confirmations[wxid] = reminder_id

        except sqlite3.Error as e:
            logger.error(f"检查服药提醒失败: {e}")
        finally:
            conn.close()

    @schedule('cron', hour=9, minute=0)
    async def check_weight_reminders(self, bot: WechatAPIClient):
        """检查体重更新提醒"""
        if not self.enable or not self.weight_reminder_enable:
            return

        # 获取所有用户
        db_path = self.get_db_path("user_info")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT wxid FROM user_info")
            users = cursor.fetchall()

            for (wxid,) in users:
                if await self.check_weight_reminder_needed(wxid):
                    await self.send_weight_reminder(bot, wxid)

        except sqlite3.Error as e:
            logger.error(f"检查体重提醒失败: {e}")
        finally:
            conn.close()

    async def send_medication_reminder(self, bot: WechatAPIClient, wxid: str,
                                     medication_name: str, reminder_id: int, chat_id: str):
        """发送服药提醒"""
        try:
            message = f"💊 服药提醒\n\n⏰ 现在是服药时间\n💊 药物：{medication_name}\n\n请服药后回复\"已服药\"确认"

            # 发送提醒消息
            await bot.send_text_message(chat_id, message)
            logger.info(f"向用户 {wxid} 发送服药提醒: {medication_name}")

        except Exception as e:
            logger.error(f"发送服药提醒失败: {e}")

    async def send_weight_reminder(self, bot: WechatAPIClient, wxid: str):
        """发送体重更新提醒"""
        try:
            user_info = await self.get_user_info(wxid)
            if not user_info:
                return

            message = f"📊 体重更新提醒\n\n您已经{self.weight_reminder_interval}天没有更新体重了\n\n请发送：更新体重 XX kg"

            # 发送到用户的私聊
            await bot.send_text_message(wxid, message)
            logger.info(f"向用户 {wxid} 发送体重更新提醒")

        except Exception as e:
            logger.error(f"发送体重提醒失败: {e}")
