[basic]
# 是否启用插件
enable = true
# 插件优先级
priority = 60
# 唤醒词
trigger_word = "扁鹊"

[ai_config]
# AI模型配置 (OpenAI兼容格式)
api_url = "https://api.llingfei.com/v1/chat/completions"
api_key = "sk-UkgLgWxe9QjCicF_FcHjv0ROl-zOIOffpJFzDoCEhT0vzEeDCS4sIct8hNs"
model_name = "gemini-2.5-pro-preview-06-05"
timeout = 30

[medication_reminder]
# 服药提醒配置
enable = true
# 未确认提醒的重复间隔（分钟）
repeat_interval = 10
# 最大重复次数
max_repeat_count = 3

[weight_reminder]
# 体重更新提醒配置
enable = true
# 提醒间隔（天）
reminder_interval = 7
# 提醒时间（小时:分钟）
reminder_time = "09:00"

[meal_analysis]
# 膳食分析配置
enable = true
# 是否启用详细营养分析
detailed_analysis = true
# 是否提供具体建议
provide_suggestions = true
