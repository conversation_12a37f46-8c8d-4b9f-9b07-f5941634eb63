# MediEat - 智能膳食建议和服药提醒插件

## 📝 插件简介

MediEat 是一个专为糖尿病和高血脂患者设计的智能膳食助手插件，提供个性化的膳食建议和服药提醒功能。

## 🌟 主要功能

### 1. 个人信息管理
- 用户基本信息存储（性别、年龄、体重、身高）
- BMI 自动计算
- 体重历史记录跟踪

### 2. 智能膳食建议
- 基于用户信息的个性化建议
- 控糖降脂专业指导
- 当日餐饮记录分析
- AI 驱动的营养评估

### 3. 服药提醒
- 定时服药提醒
- 用户确认机制
- 重复提醒功能
- 提醒记录管理

### 4. 餐饮档案管理
- 早餐、午餐、晚餐记录
- 历史餐饮查询
- 营养分析和建议

### 5. 定期体重监测
- 每7天自动提醒更新体重
- 体重变化趋势跟踪

## 💡 使用方法

### 基础设置
```
设置信息 男 25岁 70kg 175cm
```

### 体重管理
```
更新体重 70kg
更新体重 70
```

### 服药提醒
```
服药提醒 每日 08:00 二甲双胍
服药提醒 每日 20:00 阿托伐他汀
```

### 餐饮记录
```
早餐 燕麦粥、鸡蛋、牛奶
午餐 糙米饭、清蒸鱼、青菜
晚餐 小米粥、蒸蛋羹、凉拌黄瓜
```

### 查询功能
```
我的信息          # 查看个人信息
我的提醒          # 查看服药提醒列表
膳食建议          # 获取营养建议
MediEat          # 查看帮助信息
```

### 管理功能
```
删除提醒 1        # 删除指定ID的提醒
已服药           # 确认服药
```

## ⚙️ 配置说明

### config.toml 配置文件

```toml
[basic]
enable = true
priority = 60

[ai_config]
api_url = "http://localhost:11434/api/generate"
api_key = ""
model_name = "llama3.1:8b"
timeout = 30

[medication_reminder]
enable = true
repeat_interval = 10
max_repeat_count = 3

[weight_reminder]
enable = true
reminder_interval = 7
reminder_time = "09:00"

[meal_analysis]
enable = true
detailed_analysis = true
provide_suggestions = true
```

### 配置项说明

- **basic.enable**: 插件总开关
- **basic.priority**: 插件优先级
- **ai_config.api_url**: AI模型API地址
- **ai_config.model_name**: 使用的AI模型名称
- **medication_reminder.repeat_interval**: 未确认提醒的重复间隔（分钟）
- **weight_reminder.reminder_interval**: 体重提醒间隔（天）
- **meal_analysis.enable**: 是否启用膳食分析

## 🗄️ 数据存储

插件使用 SQLite 数据库存储数据，包括：

- **user_info.db**: 用户基本信息
- **medication.db**: 服药提醒记录
- **meals.db**: 餐饮记录
- **weight.db**: 体重记录和提醒

## 🎯 专业特色

### 控糖降脂专业建议
- 基于用户BMI和健康状况
- 考虑当日已摄入食物
- 提供具体的食物搭配建议
- 避免高糖高脂食物推荐

### 智能时间判断
- 根据发送时间自动判断餐饮类型
- 智能解析用户输入的时间格式
- 自动计算下次提醒时间

### 个性化体验
- 记住用户的饮食习惯
- 提供渐进式的改善建议
- 考虑用户的接受程度

## 🔧 技术特点

- 异步处理，性能优秀
- SQLite 数据库，轻量可靠
- 模块化设计，易于维护
- 完善的错误处理机制
- 支持群聊和私聊

## 📋 注意事项

1. 首次使用需要设置个人信息
2. 服药提醒需要用户主动确认
3. AI建议仅供参考，不能替代医生诊断
4. 定期更新体重有助于获得更准确的建议
5. 建议配合专业医生的治疗方案使用

## 🆘 常见问题

**Q: 如何修改已设置的个人信息？**
A: 重新发送"设置信息"命令即可覆盖原有信息。

**Q: 服药提醒没有收到怎么办？**
A: 检查提醒时间设置是否正确，确保插件已启用。

**Q: AI建议不准确怎么办？**
A: 确保个人信息完整准确，多记录几天的餐饮信息后建议会更精准。

**Q: 可以设置多个服药提醒吗？**
A: 可以，每个药物可以设置不同的提醒时间。

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**免责声明**: 本插件提供的建议仅供参考，不能替代专业医疗建议。请在医生指导下使用。
