#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def parse_blood_sugar_input(content: str) -> dict:
    """解析血糖输入"""
    result = {}
    
    # 提取血糖数值 - 使用更精确的正则表达式
    # 匹配独立的数字（前后有空格或在末尾），避免匹配类型中的数字
    value_match = re.search(r'(?:^|[\s])(\d+(?:\.\d+)?)(?:[\s]|$)', content)
    if not value_match:
        # 如果没找到，尝试匹配最后一个数字
        value_match = re.search(r'(\d+(?:\.\d+)?)(?!.*\d)', content)
    
    if value_match:
        result['value'] = float(value_match.group(1))
    
    # 判断测量类型
    content_lower = content.lower()
    if any(keyword in content_lower for keyword in ['空腹', '餐前', '早上空腹', '晨起']):
        result['type'] = '空腹血糖'
    elif any(keyword in content_lower for keyword in ['餐1', '餐后1小时', '餐后1', '1小时', '饭后1小时']):
        result['type'] = '餐1血糖'
    elif any(keyword in content_lower for keyword in ['餐2', '餐后2小时', '餐后2', '2小时', '饭后2小时']):
        result['type'] = '餐2血糖'
    elif any(keyword in content_lower for keyword in ['睡前', '晚上睡前', '临睡前']):
        result['type'] = '睡前血糖'
    else:
        result['type'] = '未知'
    
    return result

# 测试用例
test_cases = [
    "扁鹊 餐2血糖 7.2",
    "扁鹊 餐1血糖 8.5",
    "扁鹊 空腹血糖 5.8",
    "扁鹊 睡前血糖 6.9",
    "扁鹊 血糖 6.5",
    "餐2血糖 7.2",
    "餐后2小时 8.1",
    "空腹 5.6",
]

print("血糖解析测试结果：")
print("=" * 50)

for test_case in test_cases:
    result = parse_blood_sugar_input(test_case)
    print(f"输入: {test_case}")
    print(f"解析结果: 数值={result.get('value', '未识别')}, 类型={result.get('type', '未识别')}")
    print("-" * 30)
